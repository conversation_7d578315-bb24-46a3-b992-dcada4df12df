within Workspace.System.BaseCycle.XBV_2C.MVB;
model System_30XBV
  extends.Workspace.System.BaseCycle.XBV_2C.Physical.System_30XBV(
    redeclare replaceable.Workspace.System.BaseCycle.Equipement_MVB ModuleA(Selector_comp_crkA_refInd = choiceBlock.Unit.selector_Comp_CKA_refInd,Selector_comp_crkB_refInd = choiceBlock.Unit.selector_Comp_CKB_refInd,
    
    
    NcompAMax =       
    
    if (choiceBlock.isMEPS_ME and choiceBlock.isCoatingOption) then
        if ModuleA.OAT < 38 + 273.15 then
            choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKA
        elseif (ModuleA.OAT  >= 38+273.15 and ModuleA.OAT  <= 44 +273.15) then // Rampe freq_max = f(OAT) 
            ((choiceBlock.Unit.NcompMax_coating_CKA - choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKA)/(44-38))*(ModuleA.OAT -273.15) + (choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKA - ((choiceBlock.Unit.NcompMax_coating_CKA - choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKA)/(44-38)) *38)
        else 
            choiceBlock.Unit.NcompMax_coating_CKA
            
      elseif (choiceBlock.isMEPS_ME) then
    
        if ModuleA.OAT  < 38 + 273.15 then
            choiceBlock.Unit.NcompMax_MEPS_ME_CKA
        elseif (ModuleA.OAT  >= 38+273.15 and ModuleA.OAT  <= 44 +273.15) then
            ((choiceBlock.Unit.NcompMax_CKA - choiceBlock.Unit.NcompMax_MEPS_ME_CKA)/(44-38))*(ModuleA.OAT -273.15) + (choiceBlock.Unit.NcompMax_MEPS_ME_CKA - ((choiceBlock.Unit.NcompMax_CKA - choiceBlock.Unit.NcompMax_MEPS_ME_CKA)/(44-38)) *38)
        else 
            choiceBlock.Unit.NcompMax_CKA  
    
    
    
      elseif (choiceBlock.isCoatingOption)  then
        choiceBlock.Unit.NcompMax_coating_CKA
      else
        choiceBlock.Unit.NcompMax_CKA, 
     
    NcompBMax =       
    
    if (choiceBlock.isMEPS_ME and choiceBlock.isCoatingOption) then
        if ModuleA.OAT < 38 + 273.15 then
            choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKB
        elseif (ModuleA.OAT >= 38+273.15 and ModuleA.OAT <= 44 +273.15) then
            ((choiceBlock.Unit.NcompMax_coating_CKB - choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKB)/(44-38))*(ModuleA.OAT -273.15) + (choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKB - ((choiceBlock.Unit.NcompMax_coating_CKB - choiceBlock.Unit.NcompMax_MEPS_ME_coating_CKB)/(44-38)) *38)
        else 
            choiceBlock.Unit.NcompMax_coating_CKB
            
   
      elseif (choiceBlock.isMEPS_ME) then
    
        if ModuleA.OAT < 38 + 273.15 then
            choiceBlock.Unit.NcompMax_MEPS_ME_CKB
        elseif (ModuleA.OAT >= 38+273.15 and ModuleA.OAT <= 44 +273.15) then
            ((choiceBlock.Unit.NcompMax_CKB - choiceBlock.Unit.NcompMax_MEPS_ME_CKB)/(44-38))*(ModuleA.OAT-273.15) + (choiceBlock.Unit.NcompMax_MEPS_ME_CKB - ((choiceBlock.Unit.NcompMax_CKB - choiceBlock.Unit.NcompMax_MEPS_ME_CKB)/(44-38)) *38)
        else 
            choiceBlock.Unit.NcompMax_CKB    
    
        
      elseif (choiceBlock.isCoatingOption)  then
        choiceBlock.Unit.NcompMax_coating_CKB
      else
        choiceBlock.Unit.NcompMax_CKB
    
    
    ),
    redeclare Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.ChoiceBlock choiceBlock(Selector_ModuleA = Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector.Unit_30XBV_0500));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={245,166,35},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_30XBV;
