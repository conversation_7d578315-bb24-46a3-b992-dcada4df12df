within Workspace.Auxiliary.Calibration;
model CalibrationBlock_ze
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompressorA=.Workspace.Controller.Components.Types.CompressorSelector.NG1
    annotation (Dialog(group="Compressor Type",tab="Compressor"));
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompressorB=.Workspace.Controller.Components.Types.CompressorSelector.NG1
    annotation (Dialog(group="Compressor Type",tab="Compressor"));
  Integer n_A;
  Integer n_B;
  //Input
  .Modelica.Blocks.Interfaces.RealInput Re
    annotation (Placement(transformation(extent={{-89.72283959637137,96.92075901954556},{-70.27716040362863,116.36643821228833}},origin={0.0,0.0},rotation=0.0)),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={40,40}));
  .Modelica.Blocks.Interfaces.RealInput EcoFlux_A
    annotation (Placement(transformation(extent={{-89.72283959637137,76.92075901954557},{-70.27716040362863,96.36643821228832}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EcoFlux_B
    annotation (Placement(transformation(extent={{-89.72283959637137,58.92075901954556},{-70.27716040362863,78.36643821228833}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EcoArea_A
    annotation (Placement(transformation(extent={{-89.72283959637137,40.92075901954557},{-70.27716040362863,60.366438212288315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EcoArea_B
    annotation (Placement(transformation(extent={{-89.72283959637137,24.92075901954557},{-70.27716040362863,44.366438212288315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SST_A
    annotation (Placement(transformation(extent={{-89.72283959637137,6.920759019545567},{-70.27716040362863,26.366438212288312}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SST_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-9.079240980454436},{-70.27716040362863,10.366438212288315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SDT_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-27.07924098045444},{-70.27716040362863,-7.633561787711681}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SDT_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-41.07924098045443},{-70.27716040362863,-21.633561787711685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput FrqComp_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-75.07924098045443},{-70.27716040362863,-55.633561787711685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput FrqComp_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-57.07924098045443},{-70.27716040362863,-37.633561787711685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput nCoil_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-109.07924098045443},{-70.27716040362863,-89.63356178771168}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput nCoil_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-91.07924098045443},{-70.27716040362863,-71.63356178771168}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput mflowCoil_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-143.07924098045441},{-70.27716040362863,-123.63356178771164}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput mflowCoil_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-125.07924098045443},{-70.27716040362863,-105.63356178771168}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SSTmaxA
    annotation (Placement(transformation(extent={{-89.1642454197746,128.8357545802254},{-70.8357545802254,147.1642454197746}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SSTmaxB
    annotation (Placement(transformation(extent={{-88.9625350495647,113.44576944870516},{-71.0374649504353,131.37083954783458}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput LWT
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={-42.0,160.0},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput frq_fan_A
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={-18.0,160.0},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput frq_fan_B
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={8.0,160.0},rotation=-90.0)));
  //Output
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_A
    annotation (Placement(transformation(extent={{218.0,118.70769230769233},{238.0,138.70769230769233}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_B
    annotation (Placement(transformation(extent={{218.0,100.70769230769233},{238.0,120.70769230769233}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_dpc
    annotation (Placement(transformation(extent={{218.0,82.0},{238.0,102.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_A
    annotation (Placement(transformation(extent={{218.0,46.0},{238.0,66.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_B
    annotation (Placement(transformation(extent={{218.0,64.0},{238.0,84.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_U_Eco_A
    annotation (Placement(transformation(extent={{218.0,24.707692307692305},{238.0,44.707692307692305}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_U_Eco_B
    annotation (Placement(transformation(extent={{218.0,6.707692307692305},{238.0,26.707692307692305}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Power_A
    annotation (Placement(transformation(extent={{218.0,-13.292307692307695},{238.0,6.707692307692305}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Power_B
    annotation (Placement(transformation(extent={{218.0,-31.292307692307695},{238.0,-11.292307692307695}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Flow_A
    annotation (Placement(transformation(extent={{218.0,-49.292307692307695},{238.0,-29.292307692307695}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Flow_B
    annotation (Placement(transformation(extent={{218.0,-67.29230769230767},{238.0,-47.29230769230768}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_Dpr_A
    annotation (Placement(transformation(extent={{218.0,-85.29230769230767},{238.0,-65.29230769230767}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_Dpr_B
    annotation (Placement(transformation(extent={{218.0,-107.29230769230767},{238.0,-87.29230769230767}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Zfan_A
    annotation (Placement(transformation(extent={{218.0,-123.6},{238.0,-103.6}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput Zfan_B
    annotation (Placement(transformation(extent={{218.0,-139.6},{238.0,-119.6}},rotation=0.0,origin={0.0,0.0})));
  //Evap // R134a
  parameter Real a0_evap=6.59
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real a1_evap=-2.057e-2
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real a_dp=1.0
    annotation (Dialog(group="Coefficients Zdp",tab="Evaporator"));
  parameter Real b_dp=0.0
    annotation (Dialog(group="Coefficients Zdp",tab="Evaporator"));
  // ZFan
  parameter Real slope_fan=-0.17
    "Slope for Zfactor law for fan motor AC"
    annotation (Dialog(tab="Fan"));
  parameter Real intercept_fan=1.25
    "Interception for linear law for fan motor AC"
    annotation (Dialog(tab="Fan"));
  //Eco
  parameter Real a0_eco=0.322
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real a1_eco=4.394e-5
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real a2_eco=0
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real a3_eco=0
    annotation (Dialog(group="STD",tab="Eco"));
  //Compressor
  //Z_Pow
  // R1234ze
  parameter Real[3] a0_pow_ze={1.005074532,0.922015808,0.960026911}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real[3] a1_SST_pow_ze={0.002893592,0.003552713,-0.001839413}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real[3] a1_SDT_pow_ze={-0.000107637,0.000859156,0.001364326}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real[3] a1_FreqComp_pow_ze={-9.34979E-05,-0.000370807,2.38424E-05}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real Zpower_max_ze=1.08
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zpower_min_ze=0.97
    annotation (Dialog(group="Limits",tab="Compressor"));
  //Z_flow
  //R1234ze
  parameter Real[3] a0_flow_ze={0.956123203,0.935422405,0.955256835}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  parameter Real[3] a1_SDT_flow_ze={0.000500513,-0.001550021,0.001306468}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  parameter Real[3] a1_comp_flow_ze={0.00046562,0.00144408,0.000228504}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  parameter Real Zflow_max_ze=1.06
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zflow_min_ze=0.9
    annotation (Dialog(group="Limits",tab="Compressor"));
  //Condenser
  parameter Real a0_ZcondDpr=0.1689
    annotation (Dialog(tab="Condenser",group="Z_Dpr coefficient"));
  parameter Real a1_ZcondDpr=0.2115
    annotation (Dialog(tab="Condenser",group="Z_Dpr coefficient"));
  parameter Boolean is_CoatingOption=false
    " true if there is coating on both coils "
    annotation (Dialog(tab="Condenser",group="Coating Option"));
  //Limits
  parameter Real Z_dpr_Cond_max=1
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_dpr_Cond_min=0.1
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Zevap_max=0.9
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zevap_min=0.4
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zeco_min=0.3
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real Zeco_max=1.5
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real Zdp_max=0.8
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zdp_min=0.4
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zpower_max=1.08
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zpower_min=1.0
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zflow_max=1.08
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zflow_min=0.92
    annotation (Dialog(group="Limits",tab="Compressor"));
//Evaporator
equation
  Z_Evap_A=max(
    Zevap_min,
    min(
      Zevap_max,
      a1_evap*LWT+a0_evap))*SSTmaxA;
  Z_Evap_B=max(
    Zevap_min,
    min(
      Zevap_max,
      a1_evap*LWT+a0_evap))*SSTmaxB;
//Evaporator_Dpc
equation
  Z_Evap_dpc=max(
    Zdp_min,
    min(
      Zdp_max,
      a_dp*Re^b_dp));
//Batterie
equation
  Z_Cond_A=
    if is_CoatingOption then
      0.6
    else
      0.74
    "2% degradation of heat exchange at the condenser when the Coating option is activated";
  Z_Cond_B=
    if is_CoatingOption then
      0.6
    else
      0.74
    "2% degradation of heat exchange at the condenser when the Coating option is activated";
  //Z_dpr de la batterie
  Z_Cond_Dpr_A=max(
    Z_dpr_Cond_min,
    min(
      Z_dpr_Cond_max,
      a1_ZcondDpr*(mflowCoil_A/nCoil_A)+a0_ZcondDpr));
  Z_Cond_Dpr_B=max(
    Z_dpr_Cond_min,
    min(
      Z_dpr_Cond_max,
      a1_ZcondDpr*(mflowCoil_B/nCoil_B)+a0_ZcondDpr));
//Economiseur
equation
  Z_U_Eco_A=max(
    Zeco_min,
    min(
      Zeco_max,
      a3_eco*(EcoFlux_A/EcoArea_A)^3+a2_eco*(EcoFlux_A/EcoArea_A)^2+a1_eco*EcoFlux_A/EcoArea_A+a0_eco));
  Z_U_Eco_B=max(
    Zeco_min,
    min(
      Zeco_max,
      a3_eco*(EcoFlux_B/EcoArea_B)^3+a2_eco*(EcoFlux_B/EcoArea_B)^2+a1_eco*EcoFlux_B/EcoArea_B+a0_eco));
// identification du compresseur utilisÃ© pour chaque circuit 
equation
  n_A=
    if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then
      1
    elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then
      2
    else
      3;
  n_B=
    if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then
      1
    elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then
      2
    else
      3;
  //Z_Power_Compressor
  Z_Power_A=max(
    Zpower_min_ze,
    min(
      Zpower_max_ze,
      a1_SST_pow_ze[n_A]*SST_A+a1_SDT_pow_ze[n_A]*SDT_A+a1_FreqComp_pow_ze[n_A]*FrqComp_A+a0_pow_ze[n_A]));
  Z_Power_B=max(
    Zpower_min_ze,
    min(
      Zpower_max_ze,
      a1_SST_pow_ze[n_B]*SST_B+a1_SDT_pow_ze[n_B]*SDT_B+a1_FreqComp_pow_ze[n_B]*FrqComp_B+a0_pow_ze[n_B]));
  //Z_Flow_Compressor
  Z_Flow_A=min(
    Zflow_max_ze,
    max(
      Zflow_min_ze,
      a1_SDT_flow_ze[n_A]*SDT_A+a0_flow_ze[n_A]+a1_comp_flow_ze[n_A]*FrqComp_A));
  Z_Flow_B=min(
    Zflow_max_ze,
    max(
      Zflow_min_ze,
      a1_SDT_flow_ze[n_B]*SDT_B+a0_flow_ze[n_B]+a1_comp_flow_ze[n_B]*FrqComp_B));
  //ZFan
  Zfan_A=slope_fan*frq_fan_A+intercept_fan;
  Zfan_B=slope_fan*frq_fan_B+intercept_fan;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={32,50,18},
          fillPattern=FillPattern.Solid,
          extent={{-141,-141},{141,141}},
          origin={73,-3}),
        Text(
          lineColor={255,255,255},
          extent={{-143,21},{143,-21}},
          textString="CalibrationBlock",
          origin={75,-7})}));
end CalibrationBlock_ze;
