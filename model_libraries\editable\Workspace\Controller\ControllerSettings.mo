within KAV_initiation.System30KAV.Controller30KAV;
model ControllerSettings
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.CtrlSettings;
  import SI=Modelica.SIunits;
  // Import enumerations
  import Refrigerant=.Workspace.Controller.Components.Types.RefrigerantSelector;
  // Compressor enum should be also used in SystemModels
  // TODO: discuss where this enumeration should be defined
  import Compressor=.Workspace.Controller.Components.Types.CompressorSelector;
  // Import records
  import.Workspace.Controller.Components.Records.fanCurveCoefficients;
  // Import functions
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin;
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax;
  import.Workspace.Controller.Components.Functions.subcooling;
  import.Workspace.Controller.Components.Functions.fanSpeed;
  // Configuration parameters
  parameter.Modelica.SIunits.Frequency FanFrequency_max=60
    annotation (Dialog(group="Configuration"));
  parameter Refrigerant refrigerant=Refrigerant.R134a
    annotation (Dialog(group="Configuration"));
  parameter Compressor compressor=Compressor.NG1
    annotation (Dialog(group="Configuration"));
  parameter Boolean isHighAmbientOption=false
    annotation (Dialog(group="Configuration"));
  parameter SI.Frequency compressorFrequency_max=95
    annotation (Dialog(group="Configuration"));
  parameter SI.Frequency compressorFrequency_min=23
    annotation (Dialog(group="Configuration"));
  // Tuning parameters used in different setpoint calculations
  Real circuitLoad=compressorFrequency/compressorFrequency_max;
  // Compressor Capacity Setpoint, user input
  parameter SI.Power capacity_setpoint=400e3
    annotation (Dialog(group="Configuration"));
  // Compressor Low SST
  parameter SI.Temperature T_sst_min_limit_comp=273.15
    annotation (Dialog(group="Tuning"));
  // Compressor High SDT
  parameter SI.Temperature SDT_max=273.15+(
    if isHighAmbientOption then
      (
        if is60HZ and OAT_sdt_offset >(52+273.15) then
          71.5
        else
          70)
    else
      67)
    annotation (Dialog(group="Tuning"));
  SI.Temperature T_sdt_max_limit_comp;
  // Compressor High DGT
  parameter SI.Temperature T_dgt_max_limit_comp=367.15
    annotation (Dialog(group="Tuning"));
  // EXV SBC
  SI.TemperatureDifference dT_sbc_setpoint;
  parameter SI.TemperatureDifference dT_sbc_min=2
    "Minimum subcooling"
    annotation (Dialog(group="Subcooling Tuning"));
  parameter SI.TemperatureDifference dT_sbc_max=5
    "Maximum subcooling"
    annotation (Dialog(group="Subcooling Tuning"));
  parameter SI.Temperature T_oat_min=273.15-10
    annotation (Dialog(group="Subcooling Tuning"));
  parameter SI.Temperature T_oat_max=273.15+20
    annotation (Dialog(group="Subcooling Tuning"));
  parameter Real subcoolingSmoothing=0.0666
    annotation (Dialog(group="Subcooling Tuning"));
  parameter Real rel_cooler_level_setpoint=0.97
    annotation (Dialog(group="HR Tuning"));
  // EXV Low SST
  parameter SI.Temperature T_sst_min_limit_exv=T_sst_min_limit_comp+0.5
    annotation (Dialog(group="Tuning"));
  // EXV High SST
  SI.Temperature T_sst_max_limit;
  // EXV Low DSH
  SI.TemperatureDifference dT_dsh_min_limit;
  // EXV High DGT
  parameter SI.Temperature T_dgt_max_limit_exv=366.15
    annotation (Dialog(group="Tuning"));
  // pump tunning
  parameter Modelica.SIunits.Temperature EWT_setpoint=10
    "User pump setpoint"
    annotation (Dialog(group="User Pump Tuning"));
  parameter Modelica.SIunits.VolumeFlowRate Vflow_setpoint=10
    "User pump setpoint"
    annotation (Dialog(group="User Pump Tuning"));
  parameter Modelica.SIunits.Pressure extPressure_setpoint=80000
    "User pump setpoint"
    annotation (Dialog(group="User Pump Tuning"));
  // Fan optimal speed
  Real fanSpeed_setpoint;
  parameter Real[15] fanCoefficients=(
    if compressor == Compressor.NG1 then
      fanCurveCoefficients.NG1
    elseif compressor == Compressor.NG2 then
      fanCurveCoefficients.NG2
    else
      fanCurveCoefficients.NG3)
    annotation (Dialog(group="Fan Optimal Speed Tuning"));
  parameter Integer nbrCoils=8
    "Number of coils"
    annotation (Dialog(group="Fan Optimal Speed Tuning"));
  // Fan Low SDT
  SI.Temperature T_sdt_min_limit;
  // Parameters for low OAT conditions
  SI.Temperature SDT_lmap;
  parameter SI.Temperature T_oat_low=273.15-20
    annotation (Dialog(group="Fan Low SDT Tuning"));
  parameter SI.Temperature T_oat_low_sdt_act=273.15+(
    if refrigerant == Refrigerant.R134a then
      3
    else
      10)
    annotation (Dialog(group="Fan Low SDT Tuning"));
  parameter SI.TemperatureDifference deltaT=8
    annotation (Dialog(group="Fan Low SDT Tuning"));
  // Fan High SDT
  SI.Temperature T_sdt_max_limit_fan=T_sdt_max_limit_comp-2;
  // Fan High DGT
  parameter SI.Temperature T_dgt_max_limit_fan=T_dgt_max_limit_comp-2
    annotation (Dialog(group="Tuning"));
  // Economizer EXV max opening
  Real ecoEXV_max_opening
    "Max opening of Economizer EXV";
  // Economizer EXV discharge superheat
  parameter SI.TemperatureDifference dT_esh_setpoint=10
    annotation (Dialog(group="Tuning"));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_block(
    y=T_sst_max_limit)
    annotation (Placement(transformation(extent={{50.96034484924278,94.0},{81.03965515075723,114.0}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_exv_block(
    y=T_sst_min_limit_exv)
    annotation (Placement(transformation(extent={{50.96034484924277,52.0},{81.03965515075723,72.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_comp_block(
    y=T_dgt_max_limit_comp)
    annotation (Placement(transformation(extent={{50.96034484924278,52.0},{81.03965515075723,72.0}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_comp_block(
    y=T_sdt_max_limit_comp)
    annotation (Placement(transformation(extent={{50.96034484924278,9.75},{81.03965515075723,29.75}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limit_block(
    y=T_sdt_min_limit)
    annotation (Placement(transformation(extent={{50.96034484924278,-19.25},{81.03965515075723,0.75}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoint_block(
    y=dT_sbc_setpoint)
    annotation (Placement(transformation(extent={{50.96034484924278,-33.25},{81.03965515075723,-13.250000000000004}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_esh_setpoint_block(
    y=dT_esh_setpoint)
    annotation (Placement(transformation(extent={{50.96034484924278,-47.25},{81.03965515075723,-27.25}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limit_block(
    y=dT_dsh_min_limit)
    annotation (Placement(transformation(extent={{50.96034484924277,-75.25},{81.03965515075723,-55.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint_block(
    y=capacity_setpoint)
    annotation (Placement(transformation(extent={{50.96034484924278,-75.25},{81.03965515075723,-55.25}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoint_block(
    y=fanSpeed_setpoint)
    annotation (Placement(transformation(extent={{50.96034484924278,-89.25},{81.03965515075723,-69.25}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_comp_block(
    y=T_sst_min_limit_comp)
    annotation (Placement(transformation(extent={{50.96034484924278,80.0},{81.03965515075723,100.0}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fan_block(
    y=T_sdt_max_limit_fan)
    annotation (Placement(transformation(extent={{50.96034484924278,-4.25},{81.03965515075723,15.75}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_exv_block(
    y=T_dgt_max_limit_exv)
    annotation (Placement(transformation(extent={{50.96034484924277,24.0},{81.03965515075723,44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fan_block(
    y=T_dgt_max_limit_fan)
    annotation (Placement(transformation(extent={{50.96034484924278,24.0},{81.03965515075723,44.0}},origin={0,-14},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ecoEXV_max_opening_block(
    y=ecoEXV_max_opening)
    annotation (Placement(transformation(extent={{50.96034484924277,-118.0},{81.03965515075723,-98.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1Ds dT_dsh_low_lwt_linear_interpolation(
    table=[
      0.49,5;
      0.5,5;
      1,7;
      1.01,7],
    smoothness=Modelica.Blocks.Types.Smoothness.MonotoneContinuousDerivative1,
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for low DSH setpoint in EXV controller in case of R1234ze and low LWT"
    annotation (Placement(transformation(extent={{-82.0,-36.60344827586208},{-62.0,-16.60344827586208}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Tables.CombiTable1Ds T_sdt_low_map_interpolation(
    table={{-23.31,3.9},{-23.3,3.9},{-15,7.9},{5,20},{17,28.7},{22,32.3},{22.01,32.3}} .+ 273.15,
    smoothness=Modelica.Blocks.Types.Smoothness.LinearSegments,
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for low SDT setpoint in Fan controller in case of low OAT"
    annotation (Placement(transformation(extent={{-82.0,-76.0},{-62.0,-56.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Tables.CombiTable1Ds ecoEXV_max_opening_interpolation(
    table=[
      0.59,0.3;
      0.61,1.1],
    smoothness=Modelica.Blocks.Types.Smoothness.MonotoneContinuousDerivative1,
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for Economizer EXV max opening calculation"
    annotation (Placement(transformation(extent={{-82.0,-116.0},{-62.0,-96.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_setpoint_block(
    y=rel_cooler_level_setpoint)
    annotation (Placement(transformation(extent={{50.96034484924277,-132.0},{81.03965515075723,-112.0}},origin={0.0,0.0},rotation=0.0)));
  //protected
  .Modelica.Blocks.Interfaces.RealOutput T_sst
    "Suction saturated temperature"
    annotation (Placement(transformation(extent={{-80.0,90.0},{-60.0,110.0}},rotation=0.0,origin={0,-20}),iconTransformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20})));
  .Modelica.Blocks.Interfaces.RealOutput T_oat
    "Outside air temperature"
    annotation (Placement(transformation(extent={{-80.0,50.0},{-60.0,70.0}},rotation=0.0,origin={0,0}),iconTransformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20})));
  .Modelica.Blocks.Interfaces.RealOutput T_lwt
    "Leaving water temperature"
    annotation (Placement(transformation(extent={{-80.0,30.0},{-60.0,50.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20})));
  .Modelica.Blocks.Interfaces.RealOutput T_sdt
    "Suction discharge temperature"
    annotation (Placement(transformation(extent={{-80.0,10.0},{-60.0,30.0}},rotation=0.0,origin={0,0}),iconTransformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20})));
  Real deltaT_oat=T_oat-T_oat_low;
  parameter Real machineError_Temperature=0.01;
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-134.0,-20.0},{-94.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{142.0,-38.0},{182.0,2.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency
    "Compressor speed in Hz"
    annotation (Placement(transformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20}),iconTransformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20})));
  .Modelica.Blocks.Interfaces.RealOutput rel_cooler_level
    "Compressor speed in Hz"
    annotation (Placement(transformation(extent={{-80.0,108.0},{-60.0,128.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{-80.0,70.0},{-60.0,90.0}},rotation=0.0,origin={0,20})));
  .Modelica.Blocks.Sources.RealExpression VflowPumpUserSetpoint(
    y=Vflow_setpoint)
    annotation (Placement(transformation(extent={{52.87395024487225,-152.54931984425963},{82.9532605463867,-132.54931984425963}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWTSetpoint(
    y=EWT_setpoint)
    annotation (Placement(transformation(extent={{52.87395024487225,-168.54931984425963},{82.9532605463867,-148.54931984425963}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression extPressure_SetPoint(
    y=extPressure_setpoint)
    annotation (Placement(transformation(extent={{53.133134057983824,-188.9013603114808},{83.21244435949828,-168.9013603114808}},origin={0.0,0.0},rotation=0.0)));
  parameter Real OAT_sdt_offset;
  parameter Boolean is60HZ;
equation
  assert(
    refrigerant == Refrigerant.R134a or refrigerant == Refrigerant.R1234ze or refrigerant == Refrigerant.R513a,
    "Not supported refrigerant chosen.");
  assert(
    compressor == Compressor.NG1 or compressor == Compressor.NG2 or compressor == Compressor.NG3,
    "Not supported compressor chosen.");
  // Compressor High SDT
  T_sdt_max_limit_comp=min(
    SDT_max,
    1.11111*T_sst+39.46481);
  // EXV SBC
  dT_sbc_setpoint=subcooling(
    refrigerant,
    compressorFrequency,
    compressorFrequency_min,
    compressorFrequency_max,
    dT_sbc_min,
    dT_sbc_max,
    T_oat,
    T_oat_min,
    T_oat_max,
    subcoolingSmoothing);
  // EXV High SST
  //T_sst_max_limit = min(295.15, -0.36190 * T_sdt + 410.71857);
  T_sst_max_limit=295.15;
  // Extension for Data Center Application Not available for NG3 in R134a (not use now, 11 nov 2021)
  // EXV Low DSH
  dT_dsh_low_lwt_linear_interpolation.u=circuitLoad;
  if refrigerant == Refrigerant.R134a or T_lwt >= 273.15+15 then
    // R134a or R1234ze with LWT >= 15C
    dT_dsh_min_limit=7;
  else
    // R1234ze with LWT < 15C
    dT_dsh_min_limit=dT_dsh_low_lwt_linear_interpolation.y[1];
  end if;
  // Fan optimal speed
  fanSpeed_setpoint=softMax(
    0,
    softMin(
      FanFrequency_max,
      fanSpeed(
        compressorFrequency,
        nbrCoils,
        T_lwt,
        T_oat,
        fanCoefficients),
      1e-4),
    1e-4);
  // Fan Low SDT
  T_sdt_low_map_interpolation.u=T_sst;
  // Interpolation table is defined inside T_sdt_low_map_interpolation by the points 8-7-6-5 from compressor map.
  SDT_lmap=T_sdt_low_map_interpolation.y[1];
  //     if T_oat > T_oat_low then
  //         // alpha (soft factor for softMin and softMax) = 1e-4 * nominal setpoint (~300k) = 0.03
  //         T_sdt_min_limit = softMax(293.15, softMin(301.7, 0.71296 * T_sst + 94.83835, 0.03), 0.03);
  //     else  // T_oat <= T_oat_low
  //         T_sdt_min_limit = softMax(SDT_lmap, SDT_lmap + (circuitLoad * ((T_oat - T_oat_low) / (T_oat_low_sdt_act - T_oat_low)) * deltaT), 0.03);
  //     end if;
  T_sdt_min_limit=
    if T_oat-T_oat_low > machineError_Temperature then
      softMax(
        293.15,
        softMin(
          301.7,
          0.71296*T_sst+94.83835,
          0.03),
        0.03)
    else
      softMax(
        SDT_lmap,
        SDT_lmap+(circuitLoad*((T_oat-T_oat_low)/(T_oat_low_sdt_act-T_oat_low))*deltaT),
        0.03);
  // Economizer EXV max opening
  ecoEXV_max_opening_interpolation.u=circuitLoad;
  //     if T_lwt < (273.15 + 1) then
  //         ecoEXV_max_opening = ecoEXV_max_opening_interpolation.y[1];
  //     else
  //         ecoEXV_max_opening = 1.1;
  //     end if;
  ecoEXV_max_opening=
    if T_lwt-(273.15+1) <-machineError_Temperature then
      ecoEXV_max_opening_interpolation.y[1]
    else
      1.1;
  connect(rel_cooler_level,measurementBus.rel_cooler_level)
    annotation (Line(points={{-70,118},{-114,118},{-114,0}},color={0,0,127}));
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-70,80},{-114,80},{-114,0}},color={0,0,127}));
  connect(T_oat,measurementBus.T_oat)
    annotation (Line(points={{-70,60},{-114,60},{-114,0}},color={0,0,127}));
  connect(T_lwt,measurementBus.T_lwt)
    annotation (Line(points={{-70,40},{-114,40},{-114,0}},color={0,0,127}));
  connect(T_sdt,measurementBus.T_sdt)
    annotation (Line(points={{-70,20},{-114,20},{-114,0}},color={0,0,127}));
  connect(T_sst_max_limit_block.y,limitsBus.T_sst_max_limit)
    annotation (Line(points={{82.54362066583295,90},{162,90},{162,-18}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block.y,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{82.54362066583295,76},{162,76},{162,-18}},color={0,0,127}));
  connect(T_sst_min_limit_exv_block.y,limitsBus.T_sst_min_limit_exv)
    annotation (Line(points={{82.54362066583295,62},{162,62},{162,-18}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{82.54362066583295,48},{162,48},{162,-18}},color={0,0,127}));
  connect(T_dgt_max_limit_exv_block.y,limitsBus.T_dgt_max_limit_exv)
    annotation (Line(points={{82.54362066583295,34},{162,34},{162,-18}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{82.54362066583295,20},{162,20},{162,-18}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{82.54362066583295,5.75},{162,5.75},{162,-18}},color={0,0,127}));
  connect(T_sdt_max_limit_fan_block.y,limitsBus.T_sdt_max_limit_fan)
    annotation (Line(points={{82.54362066583295,-8.25},{162,-8.25},{162,-18}},color={0,0,127}));
  connect(T_sdt_min_limit_block.y,limitsBus.T_sdt_min_limit)
    annotation (Line(points={{82.54362066583295,-23.25},{162,-23.25},{162,-18}},color={0,0,127}));
  connect(dT_sbc_setpoint_block.y,limitsBus.dT_sbc_setpoint)
    annotation (Line(points={{82.54362066583295,-37.25},{162,-37.25},{162,-18}},color={0,0,127}));
  connect(dT_esh_setpoint_block.y,limitsBus.dT_esh_setpoint)
    annotation (Line(points={{82.54362066583295,-51.25},{162,-51.25},{162,-18}},color={0,0,127}));
  connect(dT_dsh_min_limit_block.y,limitsBus.dT_dsh_min_limt)
    annotation (Line(points={{82.54362066583295,-65.25},{162,-65.25},{162,-18}},color={0,0,127}));
  connect(capacity_setpoint_block.y,limitsBus.capacity_setpoint)
    annotation (Line(points={{82.54362066583295,-79.25},{162,-79.25},{162,-18}},color={0,0,127}));
  connect(fanSpeed_setpoint_block.y,limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{82.54362066583295,-93.25},{162,-93.25},{162,-18}},color={0,0,127}));
  connect(ecoEXV_max_opening_block.y,limitsBus.ecoEXV_max_opening)
    annotation (Line(points={{82.54362066583295,-108},{162,-108},{162,-18}},color={0,0,127}));
  connect(rel_cooler_level_setpoint_block.y,limitsBus.rel_cooler_level_setpoint)
    annotation (Line(points={{82.54362066583295,-122},{162,-122},{162,-18}},color={0,0,127}));
  connect(compressorFrequency,measurementBus.compressorFrequency)
    annotation (Line(points={{-70,100},{-114,100},{-114,0}},color={0,0,127}));
  connect(VflowPumpUserSetpoint.y,limitsBus.VflowPumpUserSetpoint)
    annotation (Line(points={{84.45722606146242,-142.54931984425963},{123.22861303073121,-142.54931984425963},{123.22861303073121,-18},{162,-18}},color={0,0,127}));
  connect(EWTSetpoint.y,limitsBus.EWTSetpoint)
    annotation (Line(points={{84.45722606146242,-158.54931984425963},{123.22861303073121,-158.54931984425963},{123.22861303073121,-18},{162,-18}},color={0,0,127}));
  connect(extPressure_SetPoint.y,limitsBus.extPressure_SetPoint)
    annotation (Line(points={{84.716409874574,-178.9013603114808},{123.358204937287,-178.9013603114808},{123.358204937287,-18},{162,-18}},color={0,0,127}));
  annotation (
    Placement(
      transformation(
        extent={{-80,-80},{-60,-60}})));
end ControllerSettings;
